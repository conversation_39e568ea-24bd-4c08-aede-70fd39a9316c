
# UI Kitten [<img src="https://i.imgur.com/oMcxwZ0.png" alt="Eva Design System" height="20px" />][link:eva] [![npm][badge:license]]() [![Build Status][badge:github-actions]][link:github-actions] [![Coverage Status][badge:coveralls]][link:coveralls]

[Documentation][link:doc-homepage]

UI Kitten is a React Native UI Library that allows you creating stunning multi-brand cross-platform mobile applications.
The library is based on Eva Design System which brings consistency and scalability in the design and development process.
It contains a set of general purpose UI components styled in a similar way.
And the most awesome thing: the themes can be changed in the runtime, with no need to reload the application.

100% Free and Open Source!

![link:doc-homepage](https://user-images.githubusercontent.com/1452064/115417233-212a0100-a201-11eb-8bcf-0a60cca2e081.png)

Need professional support? Visit [Akveo's React Native](https://www.akveo.com/services/mobile-application-development/react-native) experts to scale your solutions.

## What's included

- **25+ general-purpose components** designed and tested to save your time.

- **Comprehensive clear documentation** with the tons of examples.

- **Theming System -** Use Light and modern Dark themes and create your own.

- **SVG Eva Icons support -** 480+ general purpose icons

- **Eva Design System Support -** Construct an interface using basic components following Eva specifications and it will always have a stunning design.

## Starter App

**Kitten Tricks** – [react-native starter kit][link:kitten-tricks] allows you to boost the development of a mobile app.
There is a huge variety of customizable layouts, use “as is” or add new blocks.

**Over 40 screens in dark and light themes** give you the possibility to create a bright and exclusive app while saving your time on compiling numerous details.  Also, you can download the source code and use it for your own benefit.

## Quick Start

Start a new app with UI Kitten template from a scratch:

```bash
npx react-native init MyApp --template @ui-kitten/template-js
```

Or, if you want to init with TypeScript:

```bash
npx react-native init MyApp --template @ui-kitten/template-ts
```

This will setup a new React Native application configured with UI Kitten.
Refer to the [Documentation][link:doc-where-start] for more options to start.

## UI Bakery

Need to quickly build an admin panel for your mobile app? Check out UI builder [UI Bakery](https://uibakery.io).

<a href="https://uibakery.io"><img src="https://storage.uibakery.io/video-assets/landing/Logo/UIB%20400x150.png" height="80" /></a>

## How can I support the developers?
- Star our GitHub repo :star:
- Create pull requests, submit bugs, suggest new features or documentation updates :wrench:
- Read us on [Medium][link:akveo-medium]
- Follow us on [Twitter][link:akveo-twitter]
- Like our page on [Facebook][link:akveo-facebook]

## License
[MIT](LICENSE.txt) license.

## More from Akveo
- [Eva Icons][link:eva-icons] - 480+ beautiful Open Source icons

## From Developers
Made with :heart: by [Akveo team][link:akveo-homepage]. Follow us on [Twitter][link:akveo-twitter] to get the latest news first!
We're always happy to receive your feedback!

[badge:license]: https://img.shields.io/npm/l/react-native-ui-kitten.svg
[badge:github-actions]: https://github.com/akveo/react-native-ui-kitten/workflows/Build/badge.svg
[badge:coveralls]: https://coveralls.io/repos/github/akveo/react-native-ui-kitten/badge.svg?branch=master

[link:eva]: https://eva.design?utm_campaign=eva_design%20-%20home%20-%20ui_kitten%20github&utm_source=ui_kitten&utm_medium=referral&utm_content=kitten_github_readme
[link:github-actions]: https://github.com/akveo/react-native-ui-kitten/actions
[link:coveralls]: https://coveralls.io/github/akveo/react-native-ui-kitten?branch=master
[link:doc-homepage]: https://akveo.github.io/react-native-ui-kitten?utm_campaign=ui_kitten%20-%20home%20-%20ui_kitten%20github%20readme&utm_source=ui_kitten&utm_medium=referral&utm_content=homepage_link
[link:doc-where-start]: https://akveo.github.io/react-native-ui-kitten/docs/getting-started/where-to-start?utm_campaign=ui_kitten%20-%20home%20-%20ui_kitten%20github%20readme&utm_source=ui_kitten&utm_medium=referral&utm_content=where_to_start_docs_link
[link:kitten-tricks]: https://github.com/akveo/kittenTricks
[link:eva-icons]: https://github.com/akveo/eva-icons
[link:akveo-homepage]: https://www.akveo.com?utm_campaign=services%20-%20homepage%20-%20ui_kitten%20github%20readme&utm_source=ui_kitten&utm_medium=referral&utm_content=ui_kitten_readme
[link:akveo-medium]: https://medium.com/akveo-engineering
[link:akveo-twitter]: https://twitter.com/akveo
[link:akveo-facebook]: https://www.facebook.com/akveo
[link:ui-kitten-bundles]: https://store.akveo.com/collections/mobile-bundles?utm_campaign=akveo_store%20-%20mobile%20bundles%20-%20ui_kitten%20github%20readme&utm_source=ui_kitten&utm_medium=banner&utm_content=mobile_bundles_banner
[link:ui-kitten-bundle-java]: https://store.akveo.com/products/java-mobile-starter-bundle?utm_campaign=akveo_store%20-%20mobile%20bundles%20-%20ui_kitten%20github%20readme&utm_source=ui_kitten&utm_medium=referral&utm_content=java_bundle_link
[link:ui-kitten-bundle-dotnet-core]: https://store.akveo.com/products/net-core-mobile-starter-bundle?utm_campaign=akveo_store%20-%20mobile%20bundles%20-%20ui_kitten%20github%20readme&utm_source=ui_kitten&utm_medium=referral&utm_content=dotnet_bundle_link