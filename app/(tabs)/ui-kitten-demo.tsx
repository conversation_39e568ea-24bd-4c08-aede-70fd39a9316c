import React, { useState } from 'react';
import { ScrollView, StyleSheet } from 'react-native';
import {
  Layout,
  Text,
  Button,
  Card,
  Input,
  Toggle,
  Select,
  SelectItem,
  IndexPath,
  Avatar,
  Divider,
  Icon,
  TopNavigation,
  TopNavigationAction,
} from '@ui-kitten/components';

const StarIcon = (props: any) => (
  <Icon {...props} name='star' />
);

const PersonIcon = (props: any) => (
  <Icon {...props} name='person' />
);

const BackIcon = (props: any) => (
  <Icon {...props} name='arrow-back' />
);

export default function UIKittenDemoScreen() {
  const [inputValue, setInputValue] = useState('');
  const [toggleChecked, setToggleChecked] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(new IndexPath(0));

  const data = [
    'Option 1',
    'Option 2',
    'Option 3',
  ];

  const renderBackAction = () => (
    <TopNavigationAction icon={BackIcon} />
  );

  return (
    <Layout style={styles.container}>
      <TopNavigation
        title='UI Kitten Demo'
        alignment='center'
        accessoryLeft={renderBackAction}
      />
      
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.content}>
        
        {/* Typography Section */}
        <Card style={styles.card}>
          <Text category='h1'>UI Kitten Components</Text>
          <Text category='s1' style={styles.subtitle}>
            Explore the beautiful components from UI Kitten
          </Text>
        </Card>

        {/* Buttons Section */}
        <Card style={styles.card}>
          <Text category='h6' style={styles.sectionTitle}>Buttons</Text>
          <Layout style={styles.buttonContainer}>
            <Button style={styles.button} accessoryLeft={StarIcon}>
              Primary Button
            </Button>
            <Button style={styles.button} appearance='outline'>
              Outline Button
            </Button>
            <Button style={styles.button} status='success'>
              Success Button
            </Button>
            <Button style={styles.button} status='danger' size='small'>
              Danger Button
            </Button>
          </Layout>
        </Card>

        {/* Input Section */}
        <Card style={styles.card}>
          <Text category='h6' style={styles.sectionTitle}>Input & Controls</Text>
          <Input
            style={styles.input}
            placeholder='Enter your text here'
            value={inputValue}
            onChangeText={setInputValue}
            accessoryLeft={PersonIcon}
          />
          
          <Layout style={styles.controlsContainer}>
            <Toggle
              checked={toggleChecked}
              onChange={setToggleChecked}
            >
              Toggle Switch
            </Toggle>
          </Layout>

          <Select
            style={styles.select}
            placeholder='Select an option'
            value={data[selectedIndex.row]}
            selectedIndex={selectedIndex}
            onSelect={index => setSelectedIndex(index as IndexPath)}
          >
            {data.map((title, index) => (
              <SelectItem key={index} title={title} />
            ))}
          </Select>
        </Card>

        {/* Avatar & Profile Section */}
        <Card style={styles.card}>
          <Text category='h6' style={styles.sectionTitle}>Avatar & Profile</Text>
          <Layout style={styles.profileContainer}>
            <Avatar
              style={styles.avatar}
              source={{ uri: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face' }}
            />
            <Layout style={styles.profileInfo}>
              <Text category='h6'>John Doe</Text>
              <Text category='s1' appearance='hint'>Software Developer</Text>
              <Text category='c1' appearance='hint'><EMAIL></Text>
            </Layout>
          </Layout>
        </Card>

        {/* Status Cards */}
        <Card style={styles.card} status='success'>
          <Text category='h6'>Success Card</Text>
          <Text category='p2'>This is a success status card with green accent.</Text>
        </Card>

        <Card style={styles.card} status='warning'>
          <Text category='h6'>Warning Card</Text>
          <Text category='p2'>This is a warning status card with orange accent.</Text>
        </Card>

        <Card style={styles.card} status='danger'>
          <Text category='h6'>Danger Card</Text>
          <Text category='p2'>This is a danger status card with red accent.</Text>
        </Card>

      </ScrollView>
    </Layout>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  card: {
    marginBottom: 16,
  },
  subtitle: {
    marginTop: 8,
  },
  sectionTitle: {
    marginBottom: 16,
  },
  buttonContainer: {
    flexDirection: 'column',
    gap: 8,
  },
  button: {
    marginBottom: 8,
  },
  input: {
    marginBottom: 16,
  },
  controlsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  select: {
    marginBottom: 16,
  },
  profileContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    marginRight: 16,
  },
  profileInfo: {
    flex: 1,
  },
});
