import React from 'react';
import { Text as UIKittenText, TextProps } from '@ui-kitten/components';

export type UIKittenTextProps = TextProps & {
  type?: 'title' | 'subtitle' | 'default' | 'defaultSemiBold' | 'link';
};

export function UIText({ type = 'default', style, ...rest }: UIKittenTextProps) {
  const getCategory = () => {
    switch (type) {
      case 'title':
        return 'h1';
      case 'subtitle':
        return 'h6';
      case 'defaultSemiBold':
        return 's1';
      case 'link':
        return 'p1';
      default:
        return 'p1';
    }
  };

  const getAppearance = () => {
    switch (type) {
      case 'link':
        return 'hint';
      default:
        return 'default';
    }
  };

  return (
    <UIKittenText
      category={getCategory()}
      appearance={getAppearance()}
      style={style}
      {...rest}
    />
  );
}
