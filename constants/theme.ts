import * as eva from '@eva-design/eva';
import { EvaIconsPack } from '@ui-kitten/eva-icons';

// Custom theme mapping (optional - you can customize colors here)
export const customTheme = {
  ...eva.light,
  // You can override specific colors here
  // Example:
  // "color-primary-100": "#F2F6FF",
  // "color-primary-200": "#D9E4FF",
  // "color-primary-300": "#A6C1FF",
  // "color-primary-400": "#598BFF",
  // "color-primary-500": "#3366FF",
  // "color-primary-600": "#274BDB",
  // "color-primary-700": "#1A34B8",
  // "color-primary-800": "#102694",
  // "color-primary-900": "#091C7A",
};

export const customDarkTheme = {
  ...eva.dark,
  // You can override specific colors for dark theme here
};

// Icon pack configuration
export const iconsPack = EvaIconsPack;

// Theme configuration
export const lightTheme = { ...eva.light, ...customTheme };
export const darkTheme = { ...eva.dark, ...customDarkTheme };
